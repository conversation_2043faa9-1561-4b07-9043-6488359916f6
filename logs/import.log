2025-05-07 06:25:19,018 - __main__ - INFO - 程序初始化开始
2025-05-07 06:25:19,018 - __main__ - INFO - 执行初始数据导入
2025-05-07 06:25:19,018 - __main__ - INFO - 开始执行CSV导入任务 [计划时间: 每隔1小时]
2025-05-07 06:25:19,019 - src.sftp_client - INFO - 正在连接SFTP服务器 192.168.1.36:22
2025-05-07 06:25:19,198 - paramiko.transport - INFO - Connected (version 2.0, client OpenSSH_9.9)
2025-05-07 06:25:19,298 - paramiko.transport - INFO - Authentication (password) successful!
2025-05-07 06:25:19,450 - paramiko.transport.sftp - INFO - [chan 0] Opened sftp connection (server version 3)
2025-05-07 06:25:19,452 - src.sftp_client - INFO - SFTP连接成功
2025-05-07 06:25:19,452 - src.sftp_client - INFO - 正在获取远程目录 /mnt/user/Media/test/data 下的文件列表
2025-05-07 06:25:19,546 - src.sftp_client - INFO - 发现 0 个远程文件
2025-05-07 06:25:19,547 - __main__ - WARNING - 远程服务器未找到health_record.ok文件，终止本次任务
2025-05-07 06:25:19,548 - paramiko.transport.sftp - INFO - [chan 0] sftp session closed.
2025-05-07 06:25:19,549 - src.sftp_client - INFO - SFTP连接已关闭
2025-05-07 06:25:19,614 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-05-07 06:25:19,614 - __main__ - INFO - 定时任务已启动，每1小时执行一次
2025-05-07 06:25:19,615 - apscheduler.scheduler - INFO - Added job "job" to job store "default"
2025-05-07 06:25:19,616 - apscheduler.scheduler - INFO - Scheduler started
2025-05-07 06:29:16,827 - __main__ - INFO - 程序正常退出
