import os
from sqlalchemy import create_engine, text
from sqlalchemy.exc import SQLAlchemyError
from dotenv import load_dotenv
import logging

# 初始化日志
logger = logging.getLogger(__name__)

class DBClient:
    def __init__(self):
        load_dotenv()
        self.engine = create_engine(
            f"{os.getenv('DATABASE_URL')}?charset=utf8mb4",
            pool_size=5,
            max_overflow=10,
            pool_pre_ping=True
        )
        
    def create_table(self):
        """创建健康记录表"""
        create_table_sql = """
        CREATE TABLE IF NOT EXISTS health_record (
            center_id VARCHAR(255) PRIMARY KEY,
            cust_name VARCHAR(255),
            cust_birth DATE,
            cust_gender_nm VARCHAR(50),
            age INT,
            height_sf TEXT,
            weight_sf TEXT,
            diagnosis MEDIUMTEXT,
            emr MEDIUMTEXT,
            drug_plan_detail MEDIUMTEXT,
            allergy_history TEXT,
            family_history TEXT,
            labtest_report MEDIUMTEXT,
            medicalexam MEDIUMTEXT,
            risk_flag TEXT,
            jbs MEDIUMTEXT
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        """
        try:
            with self.engine.connect() as conn:
                conn.execute(text(create_table_sql))
                conn.commit()
            logger.info("健康记录表创建/验证成功")
        except SQLAlchemyError as e:
            logger.error(f"创建表失败: {str(e)}")
            raise

    def truncate_table(self):
        """清空数据表"""
        table_name = os.getenv('DB_TABLE', 'health_record')  # 保持与建表语句一致
        try:
            with self.engine.connect() as conn:
                conn.execute(text(f"TRUNCATE TABLE {table_name}"))
                conn.commit()
            logger.info(f"数据表已清空: {table_name}")
        except SQLAlchemyError as e:
            logger.error(f"清空数据表失败: {str(e)}")
            raise

    def drop_table(self):
        """删除数据表"""
        table_name = os.getenv('DB_TABLE', 'health_record')
        try:
            with self.engine.connect() as conn:
                conn.execute(text(f"DROP TABLE IF EXISTS {table_name}"))
                conn.commit()
            logger.info(f"数据表已删除: {table_name}")
        except SQLAlchemyError as e:
            logger.error(f"删除数据表失败: {str(e)}")
            raise

    def create_temp_table(self):
        """创建临时表并清空旧数据"""
        self.create_table()  # 确保原表存在
        table_name = os.getenv('DB_TABLE', 'health_record')
        temp_table = f"{table_name}_temp"
        try:
            with self.engine.begin() as conn:
                conn.execute(text(f"CREATE TABLE IF NOT EXISTS {temp_table} LIKE {table_name}"))
                conn.execute(text(f"TRUNCATE TABLE {temp_table}"))
            logger.info(f"临时表 {temp_table} 创建并清空成功")
        except SQLAlchemyError as e:
            logger.error(f"创建临时表失败: {str(e)}")
            raise

    def replace_table_with_temp(self):
        """原子化替换表操作"""
        table_name = os.getenv('DB_TABLE', 'health_record')
        temp_table = f"{table_name}_temp"
        try:
            with self.engine.begin() as conn:
                conn.execute(text(f"DROP TABLE IF EXISTS {table_name}"))
                conn.execute(text(f"ALTER TABLE {temp_table} RENAME TO {table_name}"))
            logger.info(f"表替换成功: {table_name} 已替换为 {temp_table}")
        except SQLAlchemyError as e:
            logger.error(f"替换表失败: {str(e)}")
            raise

    def cleanup_temp_table(self):
        """清理临时表"""
        table_name = os.getenv('DB_TABLE', 'health_record')
        temp_table = f"{table_name}_temp"
        try:
            with self.engine.begin() as conn:
                conn.execute(text(f"DROP TABLE IF EXISTS {temp_table}"))
            logger.info(f"临时表 {temp_table} 已清理")
        except SQLAlchemyError as e:
            logger.error(f"清理临时表失败: {str(e)}")
            raise

    def batch_upsert(self, data, table_name=None):
        """批量插入/更新数据"""
        if table_name is None:
            table_name = os.getenv('DB_TABLE', 'health_record')
        upsert_sql = f"""
        INSERT INTO {table_name} VALUES (
            :center_id, :cust_name, :cust_birth, :cust_gender_nm, :age,
            :height_sf, :weight_sf, :diagnosis, :emr, :drug_plan_detail,
            :allergy_history, :family_history, :labtest_report, :medicalexam,
            :risk_flag, :jbs
        )
        ON DUPLICATE KEY UPDATE
            cust_name=VALUES(cust_name),
            cust_birth=VALUES(cust_birth),
            cust_gender_nm=VALUES(cust_gender_nm),
            age=VALUES(age),
            height_sf=VALUES(height_sf),
            weight_sf=VALUES(weight_sf),
            diagnosis=VALUES(diagnosis),
            emr=VALUES(emr),
            drug_plan_detail=VALUES(drug_plan_detail),
            allergy_history=VALUES(allergy_history),
            family_history=VALUES(family_history),
            labtest_report=VALUES(labtest_report),
            medicalexam=VALUES(medicalexam),
            risk_flag=VALUES(risk_flag),
            jbs=VALUES(jbs)
        """
        try:
            with self.engine.connect() as conn:
                conn.execute(text(upsert_sql), data)
                conn.commit()
            logger.info(f"成功处理{len(data)}条记录")
        except SQLAlchemyError as e:
            logger.error(f"批量操作失败: {str(e)}")
            raise
