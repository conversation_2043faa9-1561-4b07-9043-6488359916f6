import logging
import os
import shutil
import stat
import paramiko
from dotenv import load_dotenv

logger = logging.getLogger(__name__)

class SFTPClient:
    def __init__(self):
        load_dotenv()
        self.host = os.getenv('SFTP_HOST')
        self.port = int(os.getenv('SFTP_PORT', 22))
        self.username = os.getenv('SFTP_USERNAME')
        self.password = os.getenv('SFTP_PASSWORD')
        self.remote_path = os.getenv('SFTP_REMOTE_PATH')
        self.local_path = os.getenv('DATA_DIR', './data')
        self.transport = None
        self.sftp = None

    def connect(self):
        """建立SFTP连接"""
        try:
            logger.info(f"正在连接SFTP服务器 {self.host}:{self.port}")
            self.transport = paramiko.Transport((self.host, self.port))
            self.transport.connect(username=self.username, password=self.password)
            self.sftp = paramiko.SFTPClient.from_transport(self.transport)
            logger.info("SFTP连接成功")
        except Exception as e:
            logger.error(f"连接失败: {str(e)}")
            raise ConnectionError(f"无法连接SFTP服务器: {str(e)}")

    def download_files(self):
        """下载所有文件并删除远程文件"""
        if not self.sftp:
            raise ConnectionError("SFTP未连接")
            
        try:
            # 清空本地目录内容（保留目录结构）
            if os.path.exists(self.local_path):
                logger.info(f"正在清空本地目录内容: {self.local_path}")
                for item in os.listdir(self.local_path):
                    item_path = os.path.join(self.local_path, item)
                    if os.path.isfile(item_path) or os.path.islink(item_path):
                        os.unlink(item_path)  # 删除文件和符号链接
                    else:
                        shutil.rmtree(item_path)  # 删除子目录
                logger.info(f"目录内容已清空: {self.local_path}")
            else:
                os.makedirs(self.local_path, exist_ok=True)
                logger.info(f"已创建本地目录: {self.local_path}")

            # 获取远程文件列表
            remote_files = self.list_remote_files()
            if not remote_files:
                logger.info("远程目录中没有需要下载的文件")
                return 0, 0

            logger.info(f"发现{len(remote_files)}个待处理文件")
            success_count = 0
            failure_count = 0

            for file in remote_files:
                remote_file = f"{self.remote_path}/{file}"
                local_file = os.path.join(self.local_path, file)
                
                try:
                    # 下载文件
                    logger.info(f"开始下载文件: {file}")
                    self.sftp.get(remote_file, local_file)
                    
                    # 验证本地文件
                    if not os.path.exists(local_file):
                        raise FileNotFoundError(f"本地文件未创建: {local_file}")
                    file_size = os.path.getsize(local_file)
                    logger.info(f"成功下载文件: {file} [大小: {file_size}字节]")
                    
                    # 删除远程文件
                    self.sftp.remove(remote_file)
                    logger.info(f"已删除远程文件: {file}")
                    success_count += 1
                    
                except Exception as e:
                    logger.error(f"文件处理失败 {file}: {str(e)}", exc_info=True)
                    failure_count += 1
                    continue

            logger.info(f"文件下载完成 [成功: {success_count} 失败: {failure_count}]")
            return success_count, failure_count

        except Exception as e:
            logger.error(f"文件下载流程异常: {str(e)}", exc_info=True)
            raise
        finally:
            self.close()

    def close(self):
        """关闭连接"""
        if self.sftp:
            self.sftp.close()
        if self.transport:
            self.transport.close()
        logger.info("SFTP连接已关闭")

    def list_remote_files(self):
        """获取远程目录下的文件列表
        
        返回:
            list: 远程文件列表，不包含目录
            异常: 抛出ConnectionError如果未连接或操作失败
        """
        if not self.sftp:
            raise ConnectionError("SFTP未连接")
            
        try:
            logger.info(f"正在获取远程目录 {self.remote_path} 下的文件列表")
            all_items = self.sftp.listdir(self.remote_path)
            
            # 过滤掉目录，只返回文件
            files = []
            for item in all_items:
                item_path = f"{self.remote_path}/{item}"
                if not self._is_directory(item_path):
                    files.append(item)
                    
            logger.info(f"发现 {len(files)} 个远程文件")
            return files
            
        except Exception as e:
            logger.error(f"获取远程文件列表失败: {str(e)}")
            raise ConnectionError(f"无法获取远程文件列表: {str(e)}")
            
    def _is_directory(self, path):
        """检查给定路径是否为目录"""
        try:
            return stat.S_ISDIR(self.sftp.stat(path).st_mode)
        except Exception:
            return False
