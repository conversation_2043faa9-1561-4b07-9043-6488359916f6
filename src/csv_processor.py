import pandas as pd
import logging
from datetime import datetime
import os
from .db_client import DBClient

logger = logging.getLogger(__name__)

class CSVProcessor:
    def __init__(self):
        self.db_client = DBClient()
        self.batch_size = int(os.getenv('BATCH_SIZE', 1000))
        self.import_dir = os.getenv('IMPORT_DIR', './data')

    def process_file(self, filepath, target_table_suffix=''):
        """处理单个CSV文件"""
        try:
            # 读取CSV文件，使用\u001E作为分隔符，指定列名
            columns = [
                'center_id', 'cust_name', 'cust_birth', 'cust_gender_nm', 'age',
                'height_sf', 'weight_sf', 'diagnosis', 'emr', 'drug_plan_detail',
                'allergy_history', 'family_history', 'labtest_report', 'medicalexam',
                'risk_flag', 'jbs'
            ]
            df = pd.read_csv(filepath, sep='\u001E', header=None, names=columns, dtype=str)
            
            # 转换日期格式
            df['cust_birth'] = pd.to_datetime(df['cust_birth'], errors='coerce')
            
            # 处理空值
            df = df.where(pd.notnull(df), None)
            
            # 转换为字典列表
            records = df.to_dict('records')
            
            # 分批处理
            for i in range(0, len(records), self.batch_size):
                batch = records[i:i + self.batch_size]
                table_name = os.getenv('DB_TABLE', 'health_record')
                if target_table_suffix:
                    table_name += target_table_suffix
                self.db_client.batch_upsert(batch, table_name=table_name)
                
            logger.info(f"成功处理文件: {filepath}, 共{len(records)}条记录")
            return True
            
        except Exception as e:
            logger.error(f"处理文件{filepath}失败: {str(e)}")
            return False

    def process_directory(self, target_table_suffix=''):
        """处理目录下所有CSV文件"""
        if not target_table_suffix:
            self.db_client.create_table()
        
        processed = 0
        failed = 0
        
        for filename in os.listdir(self.import_dir):
            if filename.startswith('health_record__'):
                filepath = os.path.join(self.import_dir, filename)
                if self.process_file(filepath, target_table_suffix):
                    processed += 1
                else:
                    failed += 1
                    
        logger.info(f"处理完成: 成功{processed}个文件, 失败{failed}个文件")
        return processed, failed
