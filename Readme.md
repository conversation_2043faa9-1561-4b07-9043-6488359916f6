# CSV增量导入MySQL工具

一个高性能的CSV数据导入工具，支持增量更新MySQL数据库记录。

## 功能特性

- ✅ 自动创建MySQL表结构(如果不存在)
- ✅ 支持特殊分隔符(\u001E)的CSV文件处理
- ✅ 增量导入(使用ON DUPLICATE KEY UPDATE)
- ✅ 定时任务执行(默认每天执行一次)
- ✅ 完善的错误处理和日志记录
- ✅ 批量插入优化性能
- ✅ 环境变量配置管理

## 技术架构

```mermaid
graph TD
    A[CSV文件] --> B[CSV处理器]
    B --> C[MySQL数据库]
    D[配置管理] --> B
    D --> C
    E[定时任务] --> B
```

## 代码结构

```
src/
├── config.rs      # 配置管理(从环境变量加载)
├── csv_processor.rs # CSV文件处理核心逻辑
├── db.rs          # 数据库操作(建表,插入)
├── error.rs       # 统一错误处理
├── lib.rs         # 模块导出
├── main.rs        # 主程序(定时任务,日志初始化)
└── model.rs       # 数据模型定义
```

## 使用说明

1. 配置环境变量(.env文件):
```bash
DATABASE_URL=mysql://user:password@host:port/database
IMPORT_DIR=./data  # CSV文件目录
BATCH_SIZE=1000    # 批量插入大小
LOG_LEVEL=info     # 日志级别
```

2. 运行程序:
```bash
```

3. 定时任务:
- 默认每天执行一次全量导入
- 支持优雅退出(Ctrl+C)

## 数据模型

处理健康记录数据，包含以下字段：
- 中心ID(主键)
- 客户姓名
- 出生日期  
- 性别
- 年龄
- 身高/体重
- 诊断信息
- 电子病历
- 药物计划
- 过敏史
- 家族病史
- 实验室报告
- 体检结果
- 风险标志
- JBS评分

## 性能优化

- 使用批量插入减少数据库往返
- 异步I/O提高吞吐量
- 错误重试机制(指数退避)

## 扩展性

- 可通过实现新的Processor支持其他数据格式
- 配置化设计便于调整参数
- 模块化结构方便添加新功能

# 重要信息
- csv使用\u001E作为分割符，示例文件health_record
- 数据表结构，其中center_id可以是主键
```SQL
CREATE TABLE IF NOT EXISTS health_record
(
    center_id        STRING,
    cust_name        STRING,
    cust_birth       DATE  ,
    cust_gender_nm   STRING,
    age              INT   ,
    height_sf        STRING,
    weight_sf        STRING,
    diagnosis        STRING,
    emr              STRING,
    drug_plan_detail STRING,
    allergy_history  STRING,
    family_history   STRING,
    labtest_report   STRING,
    medicalexam      STRING,
    risk_flag        STRING,
    jbs              STRING
) ;
```
- 测试用数据库的IP：***********:3306,其中root用户的密码是3eF5g81H0c4QLJBRLlJWrRICkuclJSLA

- 打包
```bash
tar czvf release3.tar.gz \
    --exclude='.git' \
    --exclude='.env' \
    --exclude='data/*' \
    --exclude='logs' \
    --exclude='.aider*' \
    --exclude='.env' \
    --exclude='*.pyc' \
    --exclude='__pycache__' \
    --exclude='._' \
    --exclude='.DS_Store' \
    --exclude='.gitignore' \
    .
```



podman build --platform=linux/amd64 --format=docker -t csv_importer:3.11-amd64 .