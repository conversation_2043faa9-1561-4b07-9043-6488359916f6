# 使用官方 Python 3.11 基础镜像
FROM python:3.11-slim

# 设置工作目录
WORKDIR /app

# 创建必要的目录结构
RUN mkdir -p /app/data /app/logs

# 先复制 requirements.txt 单独处理，以利用 Docker 缓存层
COPY requirements.txt .

# 安装项目依赖（仅使用官方认可的库）
RUN pip install --no-cache-dir -r requirements.txt

# 复制项目文件，排除 __pycache__ 目录
COPY main.py .
COPY src/ ./src/

# 处理环境配置文件
# 生产环境使用 env_release
COPY env_release .env
# 测试时可以通过挂载覆盖: -v $(pwd)/.env:/app/.env

# 添加非 root 用户（安全最佳实践）
# 创建非root用户
ARG USER_ID=1300
ARG GROUP_ID=1300
RUN groupadd -g $GROUP_ID user && \
    useradd -u $USER_ID -g $GROUP_ID -m user

# 设置环境变量
ENV PYTHONPATH=/app \
    PYTHONUNBUFFERED=1

# 定义容器启动时运行的命令
CMD ["python", "main.py"]

