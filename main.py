import logging
import os
from apscheduler.schedulers.blocking import BlockingScheduler
from src.csv_processor import CSVProcessor
from src.sftp_client import SFTPClient
from dotenv import load_dotenv
load_dotenv()

# 创建日志目录
os.makedirs('logs', exist_ok=True)

# 清除历史日志文件
log_file = os.path.join('logs', 'import.log')
if os.path.exists(log_file):
    os.remove(log_file)

# 初始化日志
# 获取根日志器并配置
root_logger = logging.getLogger()
root_logger.setLevel(logging.getLevelName(os.getenv('LOG_LEVEL', 'INFO')))

# 创建文件处理器
file_handler = logging.FileHandler(
    os.path.join('logs', 'import.log'),  # 使用相对路径
    mode='a',
    encoding='utf-8'
)
file_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))

# 创建控制台处理器
stream_handler = logging.StreamHandler()
stream_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))

# 清除已有处理器并添加新处理器
root_logger.handlers.clear()
root_logger.addHandler(file_handler)
root_logger.addHandler(stream_handler)

# 现在可以安全记录日志（处理器已配置完成）
# root_logger.fatal(f"当前日志等级设置为: {logging.getLevelName(root_logger.getEffectiveLevel())}")

logger = logging.getLogger(__name__)

def job():
    """定时任务执行函数"""
    logger = logging.getLogger(__name__)
    schedule_hour = int(os.getenv('SCHEDULE_HOUR', '3'))
    logger.info(f"开始执行CSV导入任务 [计划时间: 每隔{schedule_hour}小时]")
    
    try:
        # 初始化SFTP客户端
        sftp = SFTPClient()
        sftp.connect()
        
        # 检查远程文件是否存在
        remote_files = sftp.list_remote_files()
        # 过滤health开头的文件
        health_files = [f for f in remote_files if f.startswith('health_record__')]
        
        # 检查是否存在health_record.ok文件
        if 'health_record.ok' not in remote_files:
            logger.warning("远程服务器未找到health_record.ok文件，终止本次任务")
            sftp.close()
            return
            
        if not health_files:
            logger.warning("远程服务器无health_record__开头的文件，终止本次任务")
            sftp.close()
            return
            
        logger.info(f"发现{len(health_files)}个health_record__开头的文件，开始下载...")
        remote_files = health_files  # 只处理health文件
            
        logger.info(f"发现{len(remote_files)}个待处理文件，开始下载...")
        sftp.download_files()
        sftp.close()
        
        # 处理CSV文件
        processor = CSVProcessor()
        temp_table_success = False
        try:
            # 创建临时表并导入数据
            processor.db_client.create_temp_table()
            processed, failed = processor.process_directory(target_table_suffix='_temp')
            temp_table_success = True
            
            # 原子化表替换操作
            processor.db_client.replace_table_with_temp()
            logger.info(f"数据导入成功，已完成表替换: 成功处理{processed}个文件, 失败{failed}个文件")
            
        except Exception as e:
            logger.error(f"数据导入失败: {str(e)}")
            raise
        finally:
            if not temp_table_success:
                processor.db_client.cleanup_temp_table()
    except Exception as e:
        logger.error(f"任务执行失败: {str(e)}")

def main():
    logger.info("程序初始化开始")
    
    # 记录环境变量配置
    schedule_hour = int(os.getenv('SCHEDULE_HOUR', '1'))
    logger.debug(f"环境变量配置 - SCHEDULE_HOUR: {schedule_hour}")
    
    # 立即执行一次
    logger.info("执行初始数据导入")
    job()
    
    # 设置定时任务
    scheduler = BlockingScheduler()
    scheduler.add_job(
        job,
        'interval',
        hours=schedule_hour,
        timezone='Asia/Shanghai'
    )
    
    try:
        logger.info(f"定时任务已启动，每{schedule_hour}小时执行一次")
        scheduler.start()
    except (KeyboardInterrupt, SystemExit):
        logger.info("程序正常退出")
    except Exception as e:
        logger.error(f"程序异常: {str(e)}")

if __name__ == '__main__':
    main()
